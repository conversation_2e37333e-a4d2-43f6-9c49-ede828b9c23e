# Agristats System Design & Architecture Guide

## Table of Contents
1. [System Overview](#system-overview)
2. [Architecture Overview](#architecture-overview)
3. [Portal Specifications](#portal-specifications)
4. [User Roles & Permissions](#user-roles--permissions)
5. [Database Design](#database-design)
6. [Authentication & Security](#authentication--security)
7. [API Design](#api-design)
8. [User Interface Design](#user-interface-design)
9. [Data Flow](#data-flow)
10. [Technical Implementation](#technical-implementation)
11. [Deployment Architecture](#deployment-architecture)
12. [Security Considerations](#security-considerations)

## System Overview

Agristats is a comprehensive agricultural data management system designed to collect, manage, and analyze agricultural data across multiple organizations. The system operates through three distinct portals, each serving different user types and access levels.

### Core Purpose
- **Data Collection**: Streamlined agricultural data collection from field workers
- **Organization Management**: Multi-tenant organization management with role-based access
- **System Administration**: Centralized control and oversight of the entire system
- **Analytics & Reporting**: Comprehensive reporting and data analysis capabilities

### Key Features
- Multi-portal architecture with role-based access control
- Real-time and offline data collection capabilities
- Geographic location management (Country → Province → District → LLG → Ward)
- Agricultural data management (Crops, Livestock, Fertilizers, Pesticides, etc.)
- Organization-specific user management
- Comprehensive reporting and analytics

## Architecture Overview

### System Architecture Pattern
- **Multi-Portal Architecture**: Three distinct portals with shared backend
- **MVC Pattern**: CodeIgniter 4 framework following Model-View-Controller pattern
- **RESTful API Design**: Standard HTTP methods for CRUD operations
- **Role-Based Access Control (RBAC)**: Hierarchical permission system
- **Session Isolation**: Independent authentication systems for each portal

### Technology Stack
- **Backend Framework**: CodeIgniter 4
- **Database**: MySQL
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap
- **Authentication**: Session-based with password hashing
- **Security**: CSRF protection, input validation, SQL injection prevention

## Portal Specifications

### 1. Dakoii Portal (Super Admin)

#### Access Level: System-Wide Control
- **Primary Users**: System administrators, super admins
- **URL Pattern**: `/dakoii/*`
- **Authentication**: Independent login system (`dakoii_users` table)

#### Core Capabilities:
- **Multi-Organization Management**
  - Create, edit, and manage organizations
  - Monitor organization license status
  - Set organization-specific configurations
  
- **System-Wide User Management**
  - Manage portal administrators
  - Assign system-level permissions
  - Monitor user activities across all organizations

- **Geographic Location Management**
  - Manage hierarchical location data (Country → Province → District → LLG → Ward)
  - Import/export location data
  - Maintain location consistency across organizations

- **Agricultural Data Management**
  - Manage master data (crops, fertilizers, pesticides, livestock types)
  - Data validation and quality control
  - System-wide data analytics

- **System Monitoring & Analytics**
  - Dashboard with system-wide statistics
  - Performance monitoring
  - Data integrity checks
  - License and subscription management

#### Key Controllers:
- `DakoiiAuth`: Authentication and session management
- `DakoiiDashboard`: Main dashboard and analytics
- `DakoiiOrganizations`: Organization management
- `DakoiiUsers`: Portal user management
- `DakoiiLocations`: Geographic location management
- `DakoiiData`: Agricultural data management

### 2. Admin Portal (Organization Admin)

#### Access Level: Organization-Specific Administration
- **Primary Users**: Organization administrators, managers
- **URL Pattern**: `/admin/*` (to be implemented)
- **Authentication**: Organization-specific login system

#### Core Capabilities:
- **Organization User Management**
  - Create and manage field users within their organization
  - Assign roles and permissions to field workers
  - Monitor user activities and data submissions

- **Data Oversight & Quality Control**
  - Review and validate data submitted by field users
  - Generate organization-specific reports
  - Data export and analysis tools

- **Field User Assignment**
  - Assign field users to specific geographic areas
  - Manage data collection territories
  - Monitor field coverage and data collection progress

- **Organization Settings**
  - Configure organization-specific settings
  - Manage organization profile and information
  - Set data collection parameters and validation rules

#### Planned Features:
- Organization dashboard with key metrics
- Field user performance monitoring
- Data quality reports and validation tools
- Territory and assignment management
- Organization-specific reporting tools

### 3. Field Portal (Data Collection Interface)

#### Access Level: Data Collection & Submission
- **Primary Users**: Field workers, data collectors, farmers
- **URL Pattern**: `/field/*` (to be implemented)
- **Authentication**: Field user login system

#### Core Capabilities:
- **Data Collection Forms**
  - Farmer information collection
  - Crop data entry (planting, fertilizer use, harvest data)
  - Livestock data collection
  - Market and pricing information

- **Mobile-Optimized Interface**
  - Responsive design for mobile devices
  - Touch-friendly form controls
  - Offline data collection capabilities
  - GPS location capture

- **Real-time & Offline Modes**
  - Submit data when internet is available
  - Store data locally when offline
  - Automatic sync when connection is restored

- **User-Friendly Features**
  - Simple, intuitive interface design
  - Multi-language support (planned)
  - Photo capture for documentation
  - Data validation and error prevention

#### Planned Features:
- Mobile-first responsive design
- Offline data storage and sync
- GPS integration for location data
- Photo upload capabilities
- Simple data validation and error handling

## User Roles & Permissions

### Role Hierarchy

#### 1. Super Admin (Dakoii Portal)
- **Access Level**: System-wide
- **Permissions**:
  - Full system access
  - Manage all organizations
  - Create/edit/delete any data
  - System configuration
  - User management across all portals

#### 2. System Admin (Dakoii Portal)
- **Access Level**: System-wide (limited)
- **Permissions**:
  - View all organizations
  - Manage location data
  - Monitor system performance
  - Limited user management

#### 3. Organization Admin (Admin Portal)
- **Access Level**: Organization-specific
- **Permissions**:
  - Manage users within their organization
  - View/edit organization data
  - Generate organization reports
  - Assign field users to territories

#### 4. Organization Moderator (Admin Portal)
- **Access Level**: Organization-specific (limited)
- **Permissions**:
  - View organization data
  - Validate field submissions
  - Generate basic reports
  - Limited user management

#### 5. Field User (Field Portal)
- **Access Level**: Data collection only
- **Permissions**:
  - Submit data forms
  - View their own submissions
  - Edit pending submissions
  - Access assigned territories

### Permission Matrix

| Feature | Super Admin | System Admin | Org Admin | Org Moderator | Field User |
|---------|-------------|--------------|-----------|---------------|------------|
| System Configuration | ✓ | ✗ | ✗ | ✗ | ✗ |
| Manage Organizations | ✓ | View Only | Own Only | Own Only | ✗ |
| Manage Location Data | ✓ | ✓ | View Only | View Only | View Only |
| Manage Agricultural Data | ✓ | ✓ | Own Org | Own Org | Submit Only |
| User Management | All Users | View Only | Own Org | Limited | ✗ |
| Data Collection | ✓ | ✓ | ✓ | ✓ | ✓ |
| Reporting | All Data | All Data | Own Org | Own Org | Own Data |

## Database Design

### Core Tables Structure

#### Authentication & User Management
```sql
-- Dakoii Portal Users
dakoii_users (
    id, name, username, password, role, is_active, 
    created_at, updated_at
)

-- Main Application Users
users (
    id, org_id, sys_no, name, password, role, position,
    phone, email, status, created_at, updated_at
)

-- Organizations
dakoii_org (
    id, org_name, org_code, contact_person, phone, email,
    address, license_status, created_at, updated_at
)
```

#### Geographic Location Hierarchy
```sql
adx_country (id, country_name, country_code)
adx_province (id, country_id, province_name, province_code)
adx_district (id, province_id, district_name, district_code)
adx_llg (id, district_id, llg_name, llg_code)
adx_ward (id, llg_id, ward_name, ward_code)
```

#### Agricultural Data
```sql
-- Master Data
crops (id, crop_name, crop_type, description)
fertilizers (id, fertilizer_name, fertilizer_type, description)
pesticides (id, pesticide_name, pesticide_type, description)
livestock (id, livestock_name, livestock_type, description)

-- Farm Data
farmer_information (id, org_id, farmer_name, location_data, contact_info)
crops_farm_block (id, farmer_id, block_name, location_data)
crops_farm_crops_data (id, farm_block_id, crop_id, planting_data)
crops_farm_fertilizer_data (id, farm_block_id, fertilizer_id, usage_data)
```

### Database Relationships

#### One-to-Many Relationships
- `dakoii_org` → `users` (Organization has many users)
- `adx_country` → `adx_province` (Country has many provinces)
- `adx_province` → `adx_district` (Province has many districts)
- `farmer_information` → `crops_farm_block` (Farmer has many farm blocks)

#### Many-to-Many Relationships
- `users` ↔ `adx_district` (via `permissions_user_districts`)
- `crops_farm_block` ↔ `crops` (via `crops_farm_crops_data`)

## Authentication & Security

### Session Management

#### Portal Isolation
- **Dakoii Portal**: Uses `dakoii_*` session variables
- **Admin Portal**: Uses `admin_*` session variables (planned)
- **Field Portal**: Uses `field_*` session variables (planned)
- **Session Conflict Prevention**: Automatic session clearing when switching portals

#### Authentication Flow
1. **User Login**: Portal-specific login forms
2. **Credential Validation**: Password verification with hashing
3. **Session Creation**: Portal-specific session variables
4. **Permission Check**: Role-based access validation
5. **Session Maintenance**: Activity tracking and timeout management

### Security Measures

#### Password Security
- **Hashing**: PHP `password_hash()` with `PASSWORD_DEFAULT`
- **Verification**: `password_verify()` for login validation
- **Minimum Requirements**: 6+ characters (configurable)

#### Input Validation
- **CSRF Protection**: CodeIgniter 4 built-in CSRF tokens
- **SQL Injection Prevention**: Prepared statements and query builder
- **XSS Prevention**: Input sanitization and output escaping
- **Data Validation**: Server-side validation rules for all inputs

#### Access Control
- **Route Protection**: Filter-based authentication for protected routes
- **Role Verification**: Permission checks before data access
- **Session Validation**: Regular session integrity checks
- **IP Tracking**: Login attempt monitoring and logging

## API Design

### RESTful Endpoints

#### Authentication Endpoints
```
POST /dakoii/login          - Dakoii portal login
POST /admin/login           - Admin portal login
POST /field/login           - Field portal login
GET  /*/logout              - Logout from respective portal
```

#### Organization Management (Dakoii Portal)
```
GET    /dakoii/organizations        - List all organizations
POST   /dakoii/organizations        - Create new organization
GET    /dakoii/organizations/{id}   - Get organization details
PUT    /dakoii/organizations/{id}   - Update organization
DELETE /dakoii/organizations/{id}   - Delete organization
```

#### User Management
```
GET    /dakoii/users               - List portal users
POST   /dakoii/users               - Create new user
GET    /admin/users                - List organization users
POST   /admin/users                - Create organization user
```

#### Data Collection (Field Portal)
```
GET    /field/forms                - Get available forms
POST   /field/farmers              - Submit farmer data
POST   /field/crops                - Submit crop data
POST   /field/livestock            - Submit livestock data
```

### Response Format
```json
{
    "status": "success|error",
    "message": "Human readable message",
    "data": {
        // Response data
    },
    "errors": {
        // Validation errors if any
    }
}
```

## User Interface Design

### Design Principles
- **Responsive Design**: Mobile-first approach for field portal
- **Consistent UI**: Unified design language across all portals
- **Accessibility**: WCAG 2.1 compliance for inclusive design
- **Performance**: Optimized loading times and minimal resource usage

### Portal-Specific UI Guidelines

#### Dakoii Portal
- **Professional Dashboard**: Clean, data-rich interface
- **Advanced Controls**: Complex forms and data management tools
- **Analytics Focus**: Charts, graphs, and statistical displays
- **Desktop Optimized**: Designed for desktop/laptop usage

#### Admin Portal
- **Management Interface**: Balance between simplicity and functionality
- **Monitoring Tools**: User activity and data quality dashboards
- **Responsive Design**: Works on both desktop and tablet devices
- **Report Generation**: Easy-to-use reporting tools

#### Field Portal
- **Mobile-First**: Optimized for smartphones and tablets
- **Simple Forms**: Large buttons, clear labels, minimal complexity
- **Offline Capability**: Works without internet connection
- **Touch-Friendly**: Designed for touch interaction

### Common UI Components
- **Navigation**: Consistent navigation patterns across portals
- **Forms**: Standardized form controls and validation messages
- **Tables**: Responsive data tables with sorting and filtering
- **Modals**: Consistent modal dialogs for confirmations and details
- **Alerts**: Unified alert and notification system

## Data Flow

### Data Collection Flow
1. **Field User Login** → Field Portal Authentication
2. **Form Selection** → Choose data collection form
3. **Data Entry** → Fill form with agricultural data
4. **Validation** → Client-side and server-side validation
5. **Submission** → Store data in database
6. **Confirmation** → Success/error feedback to user

### Data Management Flow
1. **Admin Review** → Organization admin reviews submitted data
2. **Validation** → Data quality checks and validation
3. **Approval** → Approve or request corrections
4. **Analytics** → Data becomes available for reporting
5. **Reporting** → Generate reports and insights

### System Administration Flow
1. **Organization Setup** → Dakoii admin creates organization
2. **User Creation** → Create organization admin users
3. **Configuration** → Set organization-specific settings
4. **Monitoring** → Monitor system usage and performance
5. **Maintenance** → Regular system maintenance and updates

## Technical Implementation

### CodeIgniter 4 Structure
```
app/
├── Controllers/
│   ├── DakoiiAuth.php              # Dakoii authentication
│   ├── DakoiiDashboard.php         # Dakoii dashboard
│   ├── DakoiiOrganizations.php     # Organization management
│   ├── AdminAuth.php               # Admin portal auth (planned)
│   ├── AdminDashboard.php          # Admin dashboard (planned)
│   └── FieldAuth.php               # Field portal auth (planned)
├── Models/
│   ├── DakoiiUsersModel.php        # Dakoii users
│   ├── DakoiiOrgModel.php          # Organizations
│   ├── UsersModel.php              # Main app users
│   └── Agricultural data models...
├── Views/
│   ├── dakoii/                     # Dakoii portal views
│   ├── admin/                      # Admin portal views (planned)
│   └── field/                      # Field portal views (planned)
└── Config/
    ├── Routes.php                  # URL routing
    └── Filters.php                 # Authentication filters
```

### Database Migrations
- **Version Control**: Track database schema changes
- **Environment Sync**: Consistent database structure across environments
- **Rollback Capability**: Ability to revert database changes

### Configuration Management
- **Environment Variables**: Sensitive data in .env files
- **Feature Flags**: Enable/disable features per environment
- **Multi-Environment**: Development, staging, production configurations

## Deployment Architecture

### Environment Setup
- **Development**: Local development environment
- **Staging**: Testing environment for quality assurance
- **Production**: Live system for end users

### Server Requirements
- **Web Server**: Apache/Nginx with PHP 7.4+
- **Database**: MySQL 5.7+ or MariaDB 10.3+
- **PHP Extensions**: Required CodeIgniter 4 extensions
- **SSL Certificate**: HTTPS for secure data transmission

### Backup Strategy
- **Database Backups**: Daily automated database backups
- **File Backups**: Regular backup of uploaded files and documents
- **Version Control**: Git repository for code versioning
- **Disaster Recovery**: Documented recovery procedures

## Security Considerations

### Data Protection
- **Encryption**: Sensitive data encryption at rest and in transit
- **Access Logging**: Comprehensive audit trails for all data access
- **Data Retention**: Policies for data retention and deletion
- **Privacy Compliance**: GDPR and local privacy law compliance

### System Security
- **Regular Updates**: Keep framework and dependencies updated
- **Security Scanning**: Regular vulnerability assessments
- **Penetration Testing**: Periodic security testing
- **Incident Response**: Documented security incident procedures

### User Security
- **Password Policies**: Strong password requirements
- **Account Lockout**: Protection against brute force attacks
- **Session Security**: Secure session management
- **Two-Factor Authentication**: Optional 2FA for admin users (planned)

## Development Guidelines

### Coding Standards
- **PSR-12**: Follow PSR-12 coding standards for PHP
- **Naming Conventions**:
  - Controllers: PascalCase (e.g., `DakoiiOrganizations`)
  - Models: PascalCase with "Model" suffix (e.g., `DakoiiUsersModel`)
  - Views: snake_case with portal prefix (e.g., `dakoii_organizations_list.php`)
  - Database Tables: snake_case (e.g., `dakoii_users`, `crops_farm_data`)
- **Documentation**: PHPDoc comments for all classes and methods
- **Version Control**: Git with feature branch workflow

### Portal Development Guidelines
- **Route Naming**: Use portal prefix for all routes (`/dakoii/*`, `/admin/*`, `/field/*`)
- **Session Isolation**: Maintain separate session variables for each portal
- **Template Consistency**: Use portal-specific templates and layouts
- **Authentication**: Implement portal-specific authentication filters
- **Database Access**: Use appropriate models for each portal's data access

### Testing Strategy
- **Unit Testing**: Test individual components and models
- **Integration Testing**: Test portal interactions and data flow
- **User Acceptance Testing**: Test with actual users from each portal
- **Performance Testing**: Load testing for concurrent users
- **Security Testing**: Regular security audits and penetration testing

## Implementation Roadmap

### Phase 1: Foundation (Completed)
- ✅ Dakoii Portal basic functionality
- ✅ Authentication system with session isolation
- ✅ Organization management
- ✅ Location management
- ✅ Basic user management

### Phase 2: Admin Portal (In Progress)
- 🔄 Admin portal authentication system
- 🔄 Organization-specific user management
- 🔄 Data validation and quality control tools
- 🔄 Organization dashboard and analytics
- 🔄 Field user assignment and territory management

### Phase 3: Field Portal (Planned)
- 📋 Mobile-optimized data collection interface
- 📋 Offline data storage and synchronization
- 📋 GPS integration for location data
- 📋 Photo upload capabilities
- 📋 Simple form validation and error handling

### Phase 4: Advanced Features (Future)
- 📋 Advanced analytics and reporting
- 📋 API for third-party integrations
- 📋 Mobile applications (iOS/Android)
- 📋 Multi-language support
- 📋 Advanced security features (2FA, SSO)

## Monitoring & Maintenance

### System Monitoring
- **Performance Metrics**: Response times, database query performance
- **User Activity**: Login patterns, feature usage statistics
- **Error Tracking**: Application errors and exceptions
- **Resource Usage**: Server resources, database size, storage usage

### Maintenance Procedures
- **Regular Backups**: Automated daily backups with weekly full backups
- **Security Updates**: Monthly security patches and updates
- **Performance Optimization**: Quarterly performance reviews and optimizations
- **Data Cleanup**: Regular cleanup of old logs and temporary data

### Support & Documentation
- **User Manuals**: Comprehensive guides for each portal
- **API Documentation**: Detailed API documentation for developers
- **Troubleshooting Guides**: Common issues and solutions
- **Training Materials**: Video tutorials and training resources

## Scalability Considerations

### Horizontal Scaling
- **Load Balancing**: Multiple web servers behind load balancer
- **Database Clustering**: Master-slave database configuration
- **CDN Integration**: Content delivery network for static assets
- **Caching Strategy**: Redis/Memcached for session and data caching

### Vertical Scaling
- **Server Optimization**: Optimize server configuration for performance
- **Database Optimization**: Index optimization and query tuning
- **Code Optimization**: Profile and optimize application code
- **Resource Monitoring**: Monitor and adjust server resources as needed

### Data Management
- **Data Archiving**: Archive old data to maintain performance
- **Data Partitioning**: Partition large tables by organization or date
- **Backup Strategy**: Incremental backups for large datasets
- **Data Retention**: Implement data retention policies

## Integration Capabilities

### External System Integration
- **GIS Systems**: Integration with Geographic Information Systems
- **Weather APIs**: Weather data integration for agricultural insights
- **Market Data**: Commodity price and market information
- **Government Systems**: Integration with agricultural ministry systems

### API Development
- **RESTful APIs**: Standard REST APIs for data access
- **Authentication**: API key and OAuth2 authentication
- **Rate Limiting**: Prevent API abuse with rate limiting
- **Documentation**: Comprehensive API documentation with examples

### Data Export/Import
- **CSV Export**: Export data in CSV format for analysis
- **Excel Integration**: Direct Excel export with formatting
- **JSON/XML**: Structured data export for system integration
- **Bulk Import**: Tools for bulk data import and migration

## Compliance & Standards

### Data Privacy
- **GDPR Compliance**: European data protection regulation compliance
- **Local Privacy Laws**: Compliance with local data protection laws
- **Data Minimization**: Collect only necessary data
- **User Consent**: Clear consent mechanisms for data collection

### Agricultural Standards
- **FAO Standards**: Follow FAO guidelines for agricultural data
- **ISO Standards**: Implement relevant ISO standards for data quality
- **National Standards**: Comply with national agricultural data standards
- **Industry Best Practices**: Follow agricultural technology best practices

### Quality Assurance
- **Data Validation**: Multi-level data validation and verification
- **Audit Trails**: Comprehensive audit logs for all data changes
- **Quality Metrics**: Define and monitor data quality metrics
- **Continuous Improvement**: Regular review and improvement processes

---

## Appendices

### Appendix A: Database Schema
*Detailed database schema documentation with relationships and constraints*

### Appendix B: API Reference
*Complete API endpoint documentation with request/response examples*

### Appendix C: User Interface Mockups
*Visual designs and wireframes for all portal interfaces*

### Appendix D: Security Checklist
*Comprehensive security checklist for development and deployment*

### Appendix E: Deployment Guide
*Step-by-step deployment instructions for different environments*

---

*This document serves as the comprehensive guide for the Agristats system architecture and design. It should be updated as the system evolves and new features are implemented.*

**Document Version**: 1.0
**Last Updated**: 2025-08-05
**Next Review**: 2025-09-05
