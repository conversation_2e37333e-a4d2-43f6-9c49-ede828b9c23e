# Admin Portal Sidebar Menu Structure

## Overview
This document outlines the updated sidebar navigation structure for the Admin Portal in the Agristats system. The sidebar has been reorganized to reflect the new RESTful admin routes and provide clear separation between new admin features and legacy functionality.

## Updated Sidebar Structure

### 1. **Admin Dashboard**
```
🏠 Admin Dashboard (/admin/dashboard)
   - Main admin portal dashboard
   - Organization overview and key metrics
```

### 2. **Staff Portal Access**
```
👔 Staff Portal (/staff)
   - Quick access to staff data entry portal
   - Maintains existing staff functionality
```

### 3. **Legacy Dashboard** ⚠️
```
📊 Legacy Dashboard (/dashboard)
   - Original dashboard (to be deprecated)
   - Marked with warning color (yellow)
```

### 4. **User Management** 👥
```
👥 User Management
   ├── 👤 Organization Users (/admin/users)
   ├── 👥 User Groups (/admin/groups)
   └── 🌾 Field Users (/admin/field-users)
```

### 5. **Legacy User Management** ⚠️
```
👥 Legacy Users (to be deprecated)
   ├── 👤 Users (Legacy) (/users)
   └── 👥 Groups (Legacy) (/groups)
```

### 6. **Organization Management** 🏢
```
🏢 Organization
   ├── 📊 Dashboard (/admin/organization)
   ├── 👤 Profile (/admin/organization/profile)
   └── ⚙️ Settings (/admin/organization/settings)
```

### 7. **Data Management** 💾
```
💾 Data Management
   ├── 📊 Data Dashboard (/admin/data)
   ├── ✅ Data Validation (/admin/data/validation)
   └── 📤 Data Export (/admin/data/export)
```

### 8. **Plans Management** (Existing)
```
📋 Plans Management (/plans-management)
   - Existing functionality maintained
```

### 9. **Workplans** (Existing)
```
📝 Workplans
   ├── 📝 Manage Workplan (/workplans/manage)
   └── 👁️ Supervise Workplan (/workplans/supervise)
```

### 10. **Manage Activities** (Existing)
```
📋 Manage Activities
   ├── 📦 Manage Inputs (/workplans/activities/manage-inputs)
   ├── 🏗️ Manage Infrastructure (/workplans/activities/manage-infrastructure)
   └── 🎓 Manage Trainings (/workplans/activities/manage-trainings)
```

### 11. **Dashboard Links** (Existing Legacy)
```
📊 Farmers Dashboard (/farmers/dashboard)
📍 Location Dashboard (/location/dashboard)
```

### 12. **Specialized Dashboards** (Existing Legacy)
```
🌾 Crops Pesticides (/crops-pesticides)
🌾 Crops Harvests (/crops-harvests)
🌾 Crops Markets (/crops-markets)
🌾 Crops Diseases (/crops-diseases)
🌾 Crops Fertilizers (/crops-fertilizers)
📊 Analytics (/crop-farm-blocks-dashboard)
```

### 13. **Admin Reports** 📊
```
📊 Admin Reports
   ├── 📊 Reports Dashboard (/admin/reports)
   ├── 👨‍🌾 Farmers Report (/admin/reports/farmers)
   ├── 🌾 Crops Report (/admin/reports/crops)
   ├── 🐄 Livestock Report (/admin/reports/livestock)
   ├── 📈 Performance Report (/admin/reports/performance)
   └── 🗺️ Territory Coverage (/admin/reports/territories)
```

### 14. **Legacy Reports** ⚠️
```
📄 Legacy Reports (to be deprecated)
   ├── 👨‍🌾 Farmers Report (/reports/farmers-report)
   ├── 🛒 Crop Buyers (/reports/crop-buyers)
   └── 🏡 Farm Blocks (/reports/farm-blocks)
```

### 15. **Admin Settings** ⚙️
```
⚙️ Admin Settings
   ├── ⚙️ Settings Dashboard (/admin/settings)
   ├── 🔧 General Settings (/admin/settings/general)
   ├── ✅ Data Validation (/admin/settings/data-validation)
   └── 🔔 Notifications (/admin/settings/notifications)
```

### 16. **Logout** 🚪
```
🚪 Log Out (/logout)
   - Displays current user email/name
```

## Menu Variable Mapping

### New Admin Menu Variables
```php
// Admin Dashboard
$menu == "admin-dashboard"

// User Management
$menu == "admin-users"
$menu == "admin-groups" 
$menu == "admin-field-users"

// Organization Management
$menu == "admin-organization"
$menu == "admin-organization-profile"
$menu == "admin-organization-settings"

// Data Management
$menu == "admin-data"
$menu == "admin-data-validation"
$menu == "admin-data-export"

// Admin Reports
$menu == "admin-reports"
$menu == "admin-reports-farmers"
$menu == "admin-reports-crops"
$menu == "admin-reports-livestock"
$menu == "admin-reports-performance"
$menu == "admin-reports-territories"

// Admin Settings
$menu == "admin-settings"
$menu == "admin-settings-general"
$menu == "admin-settings-data-validation"
$menu == "admin-settings-notifications"
```

### Legacy Menu Variables (Maintained)
```php
// Existing variables maintained for backward compatibility
$menu == "dashboard"
$menu == "users"
$menu == "groups"
$menu == "reports"
$menu == "farmers-report"
// ... etc
```

## Visual Design Elements

### Color Coding
- **🟢 Green Icons**: New admin portal features (text-success)
- **🟡 Yellow Icons**: Legacy features to be deprecated (text-warning)
- **🔴 Red Background**: Logout button (bg-danger)

### Menu States
- **Active State**: Applied when current page matches menu item
- **Menu Open**: Applied to parent menus when child is active
- **Hover Effects**: Standard AdminLTE hover styling

### Icon Usage
- **fas fa-tachometer-alt**: Dashboards
- **fas fa-users**: User management
- **fas fa-building**: Organization
- **fas fa-database**: Data management
- **fas fa-chart-bar**: Admin reports
- **fas fa-file-alt**: Legacy reports
- **fas fa-cogs**: Settings
- **fas fa-sign-out-alt**: Logout

## Implementation Notes

### Template File
- **Location**: `app/Views/templates/adminlte/admindash.php`
- **Framework**: AdminLTE 3.2.0
- **Menu System**: TreeView with collapsible submenus

### Menu Activation Logic
```php
// Single menu item
<?php $active = ($menu == "admin-dashboard") ? "active" : ""; ?>

// Parent menu with children
<?= (in_array($menu, ['admin-users', 'admin-groups', 'admin-field-users'])) ? 'menu-open' : ''; ?>
```

### URL Generation
```php
// New admin routes
<a href="<?= base_url() ?>admin/users" class="nav-link">

// Legacy routes (maintained)
<a href="<?= base_url() ?>users" class="nav-link">
```

## Migration Strategy

### Phase 1: Dual Navigation (Current)
- Both new admin routes and legacy routes available
- Legacy items marked with warning colors
- Clear visual distinction between new and old

### Phase 2: Feature Migration
- Gradually move functionality from legacy to admin routes
- Update controllers to use new admin structure
- Maintain backward compatibility during transition

### Phase 3: Legacy Removal
- Remove legacy menu items once migration complete
- Clean up old routes and controllers
- Finalize admin portal structure

## Required Controllers for Menu Items

### New Admin Controllers Needed
1. `AdminAuth` - Authentication
2. `AdminDashboard` - Main dashboard
3. `AdminUsers` - User management
4. `AdminGroups` - Group management
5. `AdminOrganization` - Organization management
6. `AdminData` - Data management
7. `AdminReports` - Reporting
8. `AdminFieldUsers` - Field user management
9. `AdminSettings` - Settings management

### Menu Variable Setting
Each controller should set the appropriate `$menu` variable:
```php
$data['menu'] = 'admin-dashboard'; // or appropriate menu identifier
```

---

**Note**: This sidebar structure provides a clear, organized navigation system for the admin portal while maintaining backward compatibility with existing functionality during the migration period.
