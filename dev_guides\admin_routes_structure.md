# Admin Portal Routes Structure

## Overview
This document outlines the updated RESTful routing structure for the Admin Portal in the Agristats system. All admin routes are now organized under the `/admin` prefix with proper authentication and RESTful conventions.

## Authentication
- **Filter**: `admin_auth` (requires AdminAuth filter class)
- **Base URL**: `/admin`
- **Session Isolation**: Admin portal uses separate session variables from main app and Dakoii portal

## Route Structure

### 1. Authentication Routes
```
GET  /admin              -> AdminAuth::index (redirect to login)
GET  /admin/login        -> AdminAuth::login (show login form)
POST /admin/login        -> AdminAuth::processLogin (process login)
GET  /admin/logout       -> AdminAuth::logout (logout user)
GET  /admin/dashboard    -> AdminDashboard::index (main dashboard)
```

### 2. User Management Routes (`/admin/users`)
**RESTful CRUD Operations:**
```
GET    /admin/users              -> AdminUsers::index (list all users)
GET    /admin/users/create       -> AdminUsers::create (show create form)
POST   /admin/users              -> AdminUsers::store (store new user)
GET    /admin/users/{id}         -> AdminUsers::show (show user details)
GET    /admin/users/{id}/edit    -> AdminUsers::edit (show edit form)
PUT    /admin/users/{id}         -> AdminUsers::update (update user)
PATCH  /admin/users/{id}         -> AdminUsers::update (partial update)
DELETE /admin/users/{id}         -> AdminUsers::destroy (delete user)
```

**Form Compatibility Routes:**
```
POST /admin/users/{id}           -> AdminUsers::update_form (form update)
POST /admin/users/{id}/delete    -> AdminUsers::destroy_form (form delete)
```

**Permission Management:**
```
GET    /admin/users/{id}/permissions                    -> AdminUsers::permissions
POST   /admin/users/{id}/permissions                    -> AdminUsers::updatePermissions
POST   /admin/users/{id}/districts                      -> AdminUsers::addDistrictPermission
DELETE /admin/users/{id}/districts/{district_id}       -> AdminUsers::removeDistrictPermission
POST   /admin/users/{id}/districts/{district_id}/default -> AdminUsers::setDefaultDistrict
```

### 3. Groups Management Routes (`/admin/groups`)
**RESTful CRUD Operations:**
```
GET    /admin/groups              -> AdminGroups::index
GET    /admin/groups/create       -> AdminGroups::create
POST   /admin/groups              -> AdminGroups::store
GET    /admin/groups/{id}         -> AdminGroups::show
GET    /admin/groups/{id}/edit    -> AdminGroups::edit
PUT    /admin/groups/{id}         -> AdminGroups::update
PATCH  /admin/groups/{id}         -> AdminGroups::update
DELETE /admin/groups/{id}         -> AdminGroups::destroy
```

**Form Compatibility & API Routes:**
```
POST /admin/groups/{id}           -> AdminGroups::update_form
POST /admin/groups/{id}/delete    -> AdminGroups::destroy_form
GET  /admin/groups/api/list       -> AdminGroups::apiList
GET  /admin/groups/api/{id}       -> AdminGroups::apiShow
GET  /admin/groups/api/{id}/path  -> AdminGroups::apiPath
```

### 4. Organization Management Routes (`/admin/organization`)
```
GET  /admin/organization          -> AdminOrganization::index (dashboard)
GET  /admin/organization/profile  -> AdminOrganization::profile
GET  /admin/organization/edit     -> AdminOrganization::edit
PUT  /admin/organization          -> AdminOrganization::update
POST /admin/organization          -> AdminOrganization::update_form
GET  /admin/organization/settings -> AdminOrganization::settings
POST /admin/organization/settings -> AdminOrganization::updateSettings
```

### 5. Data Management Routes (`/admin/data`)
**Main Data Dashboard:**
```
GET /admin/data                   -> AdminData::index
```

**Data Validation & Quality Control:**
```
GET  /admin/data/validation                    -> AdminData::validation
GET  /admin/data/validation/farmers           -> AdminData::validateFarmers
GET  /admin/data/validation/crops             -> AdminData::validateCrops
GET  /admin/data/validation/livestock         -> AdminData::validateLivestock
POST /admin/data/validation/approve/{type}/{id} -> AdminData::approveData
POST /admin/data/validation/reject/{type}/{id}  -> AdminData::rejectData
```

**Data Export:**
```
GET  /admin/data/export           -> AdminData::export
POST /admin/data/export/farmers   -> AdminData::exportFarmers
POST /admin/data/export/crops     -> AdminData::exportCrops
POST /admin/data/export/livestock -> AdminData::exportLivestock
```

### 6. Reports Routes (`/admin/reports`)
```
GET  /admin/reports               -> AdminReports::index
GET  /admin/reports/farmers       -> AdminReports::farmers
GET  /admin/reports/crops         -> AdminReports::crops
GET  /admin/reports/livestock     -> AdminReports::livestock
GET  /admin/reports/performance   -> AdminReports::performance
GET  /admin/reports/territories   -> AdminReports::territories
POST /admin/reports/generate/{type} -> AdminReports::generate
GET  /admin/reports/download/{type}/{file} -> AdminReports::download
```

### 7. Field Users Management Routes (`/admin/field-users`)
**RESTful CRUD Operations:**
```
GET    /admin/field-users              -> AdminFieldUsers::index
GET    /admin/field-users/create       -> AdminFieldUsers::create
POST   /admin/field-users              -> AdminFieldUsers::store
GET    /admin/field-users/{id}         -> AdminFieldUsers::show
GET    /admin/field-users/{id}/edit    -> AdminFieldUsers::edit
PUT    /admin/field-users/{id}         -> AdminFieldUsers::update
DELETE /admin/field-users/{id}         -> AdminFieldUsers::destroy
```

**Form Compatibility:**
```
POST /admin/field-users/{id}           -> AdminFieldUsers::update_form
POST /admin/field-users/{id}/delete    -> AdminFieldUsers::destroy_form
```

**Territory & Performance Management:**
```
GET    /admin/field-users/{id}/territories           -> AdminFieldUsers::territories
POST   /admin/field-users/{id}/territories           -> AdminFieldUsers::assignTerritories
DELETE /admin/field-users/{id}/territories/{territory_id} -> AdminFieldUsers::removeTerritoryAssignment
GET    /admin/field-users/{id}/performance           -> AdminFieldUsers::performance
GET    /admin/field-users/{id}/activities            -> AdminFieldUsers::activities
```

### 8. Settings Routes (`/admin/settings`)
```
GET  /admin/settings                    -> AdminSettings::index
GET  /admin/settings/general            -> AdminSettings::general
POST /admin/settings/general            -> AdminSettings::updateGeneral
GET  /admin/settings/data-validation    -> AdminSettings::dataValidation
POST /admin/settings/data-validation    -> AdminSettings::updateDataValidation
GET  /admin/settings/notifications      -> AdminSettings::notifications
POST /admin/settings/notifications      -> AdminSettings::updateNotifications
```

## Authentication Filter Configuration

### Filter Aliases (app/Config/Filters.php)
```php
'admin_auth' => \App\Filters\AdminAuth::class,
```

### Filter Rules
```php
'admin_auth' => [
    'before' => [
        'admin/dashboard',
        'admin/users/*',
        'admin/groups/*',
        'admin/organization/*',
        'admin/data/*',
        'admin/reports/*',
        'admin/field-users/*',
        'admin/settings/*'
    ]
]
```

### Global Filter Exclusions
```php
'auth' => [
    'except' => [
        '/',
        'login',
        'about',
        'staff_login',
        'admin',
        'admin/*',    // Exclude ALL Admin routes from main auth
        'dakoii',
        'dakoii/*',   // Exclude ALL Dakoii routes from main auth
        'assets/*',
        'public/*'
    ]
]
```

## Legacy Routes Status

### Routes Marked for Migration
The following legacy routes are still active but marked for migration to the admin portal:

1. **Dashboards Routes** (`/dashboards/*`) → Move to `/admin/reports`
2. **Specialized Dashboard Routes** (`/crops-*`) → Move to `/admin/reports`
3. **Farmers Dashboard Routes** (`/farmers/*`) → Move to `/admin/reports`
4. **Location Dashboard Routes** (`/location/*`) → Move to `/admin/reports`
5. **Reports Routes** (`/reports/*`) → Move to `/admin/reports`

### Migration Notes
- All legacy routes now have `['filter' => 'auth']` applied
- Legacy routes are clearly marked with comments for future migration
- New admin routes follow strict RESTful conventions
- Session isolation prevents conflicts between portals

## Required Controllers

The following controllers need to be created for the admin portal:

1. `AdminAuth` - Authentication and session management
2. `AdminDashboard` - Main admin dashboard
3. `AdminUsers` - Organization user management
4. `AdminGroups` - Group management
5. `AdminOrganization` - Organization profile and settings
6. `AdminData` - Data validation and export
7. `AdminReports` - Reporting and analytics
8. `AdminFieldUsers` - Field user management
9. `AdminSettings` - Admin portal settings

## Required Filter

Create `app/Filters/AdminAuth.php` for admin portal authentication similar to the existing `DakoiiAuth` filter.

---

**Note**: This structure provides a clean, RESTful admin portal that is completely separate from both the main application and the Dakoii portal, ensuring proper session isolation and role-based access control.
