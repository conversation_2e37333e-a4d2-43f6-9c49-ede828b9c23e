<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class AdminAuth implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        $session = \Config\Services::session();

        // First check if user is logged in
        if (!$session->has('logged_in') || !$session->has('name')) {
            return redirect()->to('login')->with('error', 'Please login to continue');
        }

        // Check if user has admin privileges (is_admin = 1)
        if (!$session->has('is_admin') || $session->get('is_admin') != 1) {
            // User is logged in but not an admin
            $userRole = $session->get('role');
            
            // Redirect based on role
            if ($userRole == 'user' || $userRole == 'guest') {
                return redirect()->to('staff')->with('error', 'Access denied. Admin privileges required.');
            } else {
                return redirect()->to('login')->with('error', 'Access denied. Admin privileges required.');
            }
        }

        // Optional: Add additional role-based access control
        if (isset($arguments[0])) {
            $requiredRole = $arguments[0];
            if ($session->get('role') !== $requiredRole) {
                return redirect()->to('dashboard')->with('error', 'Access denied. Insufficient privileges.');
            }
        }
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // No after-filter needed
    }
}
